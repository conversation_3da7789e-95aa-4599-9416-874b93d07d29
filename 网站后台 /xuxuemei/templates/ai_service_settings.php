<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 只有一个AI设置页面，不需要标签页参数
?>

<div class="ai-service-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-robot"></i> AI客服设置</h1>
        <p>配置DeepSeek AI智能客服功能</p>
    </div>

    <!-- 顶部导航标签 -->
    <div class="ai-service-nav">
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="deepseek_settings">
                <i class="fas fa-brain"></i>
                <span>DeepSeek</span>
            </button>
            <button class="nav-tab" data-tab="doubao_settings">
                <i class="fas fa-robot"></i>
                <span>豆包</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="ai-service-content">
        <!-- DeepSeek设置模块 -->
        <div id="deepseek_settings-content" class="tab-content active">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-cog"></i> AI智能回复设置</h3>
                </div>
                <div class="card-body">
                    <!-- AI开关 -->
                    <div class="form-group">
                        <div class="ai-toggle-container">
                            <label class="switch">
                                <input type="checkbox" id="aiToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">启用AI回复</span>
                        </div>
                    </div>

                    <!-- API密钥管理 -->
                    <div class="form-group">
                        <label class="form-label">API密钥：</label>
                        <div class="api-keys-container">
                            <div class="api-key-input-row">
                                <input type="password" class="form-input" id="aiApiKeyInput" placeholder="输入DeepSeek API密钥">
                                <button type="button" class="btn btn-primary" id="saveApiKeyBtn">保存</button>
                            </div>
                            <div class="api-keys-status" id="apiKeysStatus">
                                <!-- API密钥状态显示区域 -->
                            </div>
                        </div>
                    </div>

                    <!-- 模型选择 -->
                    <div class="form-group">
                        <label class="form-label">模型：</label>
                        <select class="form-select" id="aiModelSelect">
                            <option value="deepseek-chat">DeepSeek Chat</option>
                            <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                        </select>
                    </div>

                    <!-- 深度思考功能 -->
                    <div class="form-group deep-thinking-section">
                        <div class="deep-thinking-toggle">
                            <label class="switch">
                                <input type="checkbox" id="deepThinkingToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">深度思考 (R1)</span>
                        </div>
                        <div class="deep-thinking-info">仅对DeepSeek-R1-0528模型有效</div>
                    </div>

                    <!-- 回复延迟 -->
                    <div class="form-group">
                        <label class="form-label">回复延迟：</label>
                        <input type="number" min="0" max="60" class="form-input" id="aiReplyDelayInput" placeholder="秒（0为立即回复）">
                    </div>

                    <!-- 系统提示词 -->
                    <div class="form-group">
                        <label class="form-label">系统提示词：</label>
                        <textarea class="form-textarea" id="aiSystemPrompt" rows="4" placeholder="设置AI的角色和回复风格">你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。</textarea>
                    </div>

                    <!-- AI状态显示 -->
                    <div class="form-group">
                        <div class="ai-status-display">
                            <div class="status-indicator" id="aiStatusIndicator"></div>
                            <span class="status-text" id="aiStatusText">AI已禁用</span>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-success" id="saveAiSettingsBtn">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                        <button type="button" class="btn btn-secondary" id="testAiConnectionBtn">
                            <i class="fas fa-plug"></i> 测试连接
                        </button>
                    </div>
                </div>
            </div>

            <!-- DeepSeek管理模块 -->
            <div class="settings-card management-card">
                <div class="card-header">
                    <h3><i class="fas fa-cogs"></i> DeepSeek管理</h3>
                    <p class="card-description">管理已保存的DeepSeek设置配置</p>
                </div>
                <div class="card-body">
                    <div class="management-container" id="deepseekManagementContainer">
                        <!-- DeepSeek设置管理列表将在这里显示 -->
                        <div class="no-settings-message" id="deepseekNoSettings">
                            <i class="fas fa-info-circle"></i>
                            <span>暂无保存的DeepSeek设置配置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 豆包设置模块 -->
        <div id="doubao_settings-content" class="tab-content">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-cog"></i> 豆包AI智能回复设置</h3>
                </div>
                <div class="card-body">
                    <!-- AI开关 -->
                    <div class="form-group">
                        <div class="ai-toggle-container">
                            <label class="switch">
                                <input type="checkbox" id="doubaoToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">启用豆包AI回复</span>
                        </div>
                    </div>

                    <!-- API密钥管理 -->
                    <div class="form-group">
                        <label class="form-label">API密钥：</label>
                        <div class="api-keys-container">
                            <div class="api-key-input-row">
                                <input type="password" class="form-input" id="doubaoApiKeyInput" placeholder="输入豆包API密钥">
                                <button type="button" class="btn btn-primary" id="saveDoubaoApiKeyBtn">保存</button>
                            </div>
                            <div class="api-keys-status" id="doubaoApiKeysStatus">
                                <!-- API密钥状态显示区域 -->
                            </div>
                        </div>
                    </div>

                    <!-- 模型选择 -->
                    <div class="form-group">
                        <label class="form-label">模型：</label>
                        <select class="form-select" id="doubaoModelSelect">
                            <option value="doubao-seed-1-6-250615">豆包 Seed 1.6</option>
                            <option value="doubao-1.5-vision-pro-250328">豆包 1.5 Vision Pro</option>
                            <option value="doubao-seed-1-6-thinking-250715">豆包 Seed 1.6 Thinking</option>
                        </select>
                    </div>

                    <!-- 思考功能 -->
                    <div class="form-group thinking-section">
                        <div class="thinking-toggle">
                            <label class="switch">
                                <input type="checkbox" id="doubaoThinkingToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">深度思考模式</span>
                        </div>
                        <div class="thinking-info">仅对豆包 Seed 1.6 Thinking模型有效</div>
                    </div>

                    <!-- 回复延迟 -->
                    <div class="form-group">
                        <label class="form-label">回复延迟：</label>
                        <input type="number" min="0" max="60" class="form-input" id="doubaoReplyDelayInput" placeholder="秒（0为立即回复）">
                    </div>

                    <!-- 系统提示词 -->
                    <div class="form-group">
                        <label class="form-label">系统提示词：</label>
                        <textarea class="form-textarea" id="doubaoSystemPrompt" rows="4" placeholder="设置豆包AI的角色和回复风格">你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。</textarea>
                    </div>

                    <!-- AI状态显示 -->
                    <div class="form-group">
                        <div class="ai-status-display">
                            <div class="status-indicator" id="doubaoStatusIndicator"></div>
                            <span class="status-text" id="doubaoStatusText">豆包AI已禁用</span>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-success" id="saveDoubaoSettingsBtn">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                        <button type="button" class="btn btn-secondary" id="testDoubaoConnectionBtn">
                            <i class="fas fa-plug"></i> 测试连接
                        </button>
                    </div>
                </div>
            </div>

            <!-- 豆包管理模块 -->
            <div class="settings-card management-card">
                <div class="card-header">
                    <h3><i class="fas fa-cogs"></i> 豆包管理</h3>
                    <p class="card-description">管理已保存的豆包设置配置</p>
                </div>
                <div class="card-body">
                    <div class="management-container" id="doubaoManagementContainer">
                        <!-- 豆包设置管理列表将在这里显示 -->
                        <div class="no-settings-message" id="doubaoNoSettings">
                            <i class="fas fa-info-circle"></i>
                            <span>暂无保存的豆包设置配置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API密钥管理弹窗 -->
<div class="api-keys-modal" id="apiKeysModal" style="display: none;">
    <div class="api-keys-modal-content">
        <div class="api-keys-modal-header">
            <h3>API密钥管理</h3>
            <button class="modal-close-btn" id="closeApiKeysModal">&times;</button>
        </div>
        <div class="api-keys-modal-body">
            <div class="api-keys-list" id="apiKeysModalList">
                <!-- API密钥列表将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 豆包API密钥管理弹窗 -->
<div class="api-keys-modal" id="doubaoApiKeysModal" style="display: none;">
    <div class="api-keys-modal-content">
        <div class="api-keys-modal-header">
            <h3>豆包API密钥管理</h3>
            <button class="modal-close-btn" id="closeDoubaoApiKeysModal">&times;</button>
        </div>
        <div class="api-keys-modal-body">
            <div class="api-keys-list" id="doubaoApiKeysModalList">
                <!-- 豆包API密钥列表将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 通知消息 -->
<div id="notification" class="notification" style="display: none;"></div>

<!-- AI客服设置专用样式 -->
<style>
.ai-service-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

/* 顶部导航标签 */
.ai-service-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-2px);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.4);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-tab i {
    font-size: 18px;
}

/* 主内容区域 */
.ai-service-content {
    min-height: 400px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 设置卡片 */
.settings-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 25px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input::placeholder, .form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.ai-toggle-container, .deep-thinking-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toggle-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

/* API密钥管理 */
.api-keys-container {
    width: 100%;
}

.api-key-input-row {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.api-key-input-row .form-input {
    flex: 1;
}

.api-keys-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.api-keys-status-display {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.api-keys-status-display:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

/* API密钥列表容器 */
.api-keys-list-container {
    margin-top: 10px;
}

.api-keys-header {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.api-keys-list {
    max-height: 300px;
    overflow-y: auto;
}

.api-key-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.api-key-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.api-key-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.api-key-text {
    color: rgba(255, 255, 255, 0.9);
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.api-key-status {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.api-key-status.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.api-key-status.error {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.api-key-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
}

/* 深度思考功能 */
.deep-thinking-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.deep-thinking-info {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 8px;
    font-style: italic;
}

/* 豆包思考功能 */
.thinking-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.thinking-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
}

.thinking-info {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 8px;
    font-style: italic;
}

/* AI状态显示 */
.ai-status-display {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #FA5151;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.status-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #2196F3;
    color: white;
}

.btn-primary:hover {
    background: #1976D2;
    transform: translateY(-1px);
}

.btn-success {
    background: #4CAF50;
    color: white;
}

.btn-success:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 信息文本 */
.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-align: center;
    padding: 40px 20px;
}

/* API密钥弹窗样式 */
.api-keys-modal {
    position: fixed;
    z-index: 10003;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.api-keys-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.api-keys-modal-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.api-keys-modal-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
}

.modal-close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.api-keys-modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.api-key-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.api-key-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.api-key-text {
    color: white;
    font-family: monospace;
    font-size: 14px;
}

.api-key-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.api-key-status.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.api-key-status.error {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.api-key-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #d32f2f;
}

.empty-api-keys {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    padding: 40px 20px;
    font-size: 14px;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.info {
    background: #2196F3;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 管理模块样式 */
.management-card {
    margin-top: 20px;
    border: 2px solid rgba(255, 255, 255, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.05));
}

.management-card .card-header {
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.3), rgba(80, 200, 120, 0.3));
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.management-card .card-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 5px 0 0 0;
    font-weight: normal;
}

.management-container {
    min-height: 100px;
}

.no-settings-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
    text-align: center;
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
}

.no-settings-message i {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.5);
}

.settings-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.settings-item:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.settings-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.settings-item-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-item-actions {
    display: flex;
    gap: 10px;
}

.settings-item-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.settings-detail {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
}

.settings-detail-value {
    color: white;
    font-size: 14px;
    word-break: break-all;
}

.settings-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.settings-status.enabled {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.settings-status.disabled {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.btn-edit {
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #1976D2, #0097A7);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #F44336, #E91E63);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #D32F2F, #C2185B);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-service-container {
        padding: 15px;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 2px;
    }

    .nav-tab {
        padding: 12px 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .api-key-input-row {
        flex-direction: column;
    }

    .settings-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .settings-item-content {
        grid-template-columns: 1fr;
    }

    .settings-item-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
</style>

<!-- AI客服设置专用JavaScript -->
<script>
// AI设置数据
let aiSettings = {
    // DeepSeek设置
    deepseek: {
        enabled: false,
        apiKeys: [],
        apiKeyStatus: [],
        currentApiKeyIndex: 0,
        model: 'deepseek-chat',
        deepThinkingEnabled: false,
        replyDelay: 0,
        systemPrompt: '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
    },
    // 豆包设置
    doubao: {
        enabled: false,
        apiKeys: [],
        apiKeyStatus: [],
        currentApiKeyIndex: 0,
        model: 'doubao-seed-1-6-250615',
        thinkingEnabled: false,
        replyDelay: 0,
        systemPrompt: '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
    }
};

// 当前活动的标签页
let currentTab = 'deepseek_settings';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabSwitching();
    restoreTabState(); // 恢复标签页状态
    initializeAISettings();
    loadAISettings();
    bindEvents();
});

// 初始化标签页切换功能
function initializeTabSwitching() {
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            navTabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab + '-content').classList.add('active');

            // 更新当前标签页
            currentTab = targetTab;

            // 保存当前标签页状态到本地存储
            localStorage.setItem('currentAITab', currentTab);

            // 重新初始化当前标签页的设置
            if (targetTab === 'deepseek_settings') {
                initializeDeepSeekSettings();
            } else if (targetTab === 'doubao_settings') {
                initializeDoubaoSettings();
            }
        });
    });
}

// 恢复标签页状态
function restoreTabState() {
    // 从本地存储获取上次的标签页状态
    const savedTab = localStorage.getItem('currentAITab');
    if (savedTab && (savedTab === 'deepseek_settings' || savedTab === 'doubao_settings')) {
        currentTab = savedTab;
    } else {
        currentTab = 'deepseek_settings'; // 默认显示DeepSeek
    }

    // 更新UI状态
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    // 移除所有活动状态
    navTabs.forEach(t => t.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // 设置当前活动标签页
    const activeTab = document.querySelector(`[data-tab="${currentTab}"]`);
    const activeContent = document.getElementById(currentTab + '-content');

    if (activeTab) activeTab.classList.add('active');
    if (activeContent) activeContent.classList.add('active');

    console.log(`恢复标签页状态: ${currentTab}`);
}

// 初始化AI设置（通用）
function initializeAISettings() {
    initializeDeepSeekSettings();
    initializeDoubaoSettings();
}

// 初始化DeepSeek设置
function initializeDeepSeekSettings() {
    const elements = {
        aiToggle: document.getElementById('aiToggle'),
        aiApiKeyInput: document.getElementById('aiApiKeyInput'),
        saveApiKeyBtn: document.getElementById('saveApiKeyBtn'),
        aiModelSelect: document.getElementById('aiModelSelect'),
        deepThinkingToggle: document.getElementById('deepThinkingToggle'),
        aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
        aiSystemPrompt: document.getElementById('aiSystemPrompt'),
        saveAiSettingsBtn: document.getElementById('saveAiSettingsBtn'),
        testAiConnectionBtn: document.getElementById('testAiConnectionBtn'),
        apiKeysStatus: document.getElementById('apiKeysStatus'),
        aiStatusIndicator: document.getElementById('aiStatusIndicator'),
        aiStatusText: document.getElementById('aiStatusText')
    };

    // 设置初始值
    if (elements.aiToggle) elements.aiToggle.checked = aiSettings.deepseek.enabled;
    if (elements.aiModelSelect) elements.aiModelSelect.value = aiSettings.deepseek.model;
    if (elements.deepThinkingToggle) elements.deepThinkingToggle.checked = aiSettings.deepseek.deepThinkingEnabled;
    if (elements.aiReplyDelayInput) elements.aiReplyDelayInput.value = aiSettings.deepseek.replyDelay;
    if (elements.aiSystemPrompt) elements.aiSystemPrompt.value = aiSettings.deepseek.systemPrompt;

    updateDeepSeekStatus();
    renderDeepSeekApiKeysList();
}

// 初始化豆包设置
function initializeDoubaoSettings() {
    const elements = {
        doubaoToggle: document.getElementById('doubaoToggle'),
        doubaoApiKeyInput: document.getElementById('doubaoApiKeyInput'),
        saveDoubaoApiKeyBtn: document.getElementById('saveDoubaoApiKeyBtn'),
        doubaoModelSelect: document.getElementById('doubaoModelSelect'),
        doubaoThinkingToggle: document.getElementById('doubaoThinkingToggle'),
        doubaoReplyDelayInput: document.getElementById('doubaoReplyDelayInput'),
        doubaoSystemPrompt: document.getElementById('doubaoSystemPrompt'),
        saveDoubaoSettingsBtn: document.getElementById('saveDoubaoSettingsBtn'),
        testDoubaoConnectionBtn: document.getElementById('testDoubaoConnectionBtn'),
        doubaoApiKeysStatus: document.getElementById('doubaoApiKeysStatus'),
        doubaoStatusIndicator: document.getElementById('doubaoStatusIndicator'),
        doubaoStatusText: document.getElementById('doubaoStatusText')
    };

    // 设置初始值
    if (elements.doubaoToggle) elements.doubaoToggle.checked = aiSettings.doubao.enabled;
    if (elements.doubaoModelSelect) elements.doubaoModelSelect.value = aiSettings.doubao.model;
    if (elements.doubaoThinkingToggle) elements.doubaoThinkingToggle.checked = aiSettings.doubao.thinkingEnabled;
    if (elements.doubaoReplyDelayInput) elements.doubaoReplyDelayInput.value = aiSettings.doubao.replyDelay;
    if (elements.doubaoSystemPrompt) elements.doubaoSystemPrompt.value = aiSettings.doubao.systemPrompt;

    updateDoubaoStatus();
    renderDoubaoApiKeysList();
}

// 绑定事件
function bindEvents() {
    bindDeepSeekEvents();
    bindDoubaoEvents();
}

// 绑定DeepSeek事件
function bindDeepSeekEvents() {
    const elements = {
        aiToggle: document.getElementById('aiToggle'),
        saveApiKeyBtn: document.getElementById('saveApiKeyBtn'),
        aiModelSelect: document.getElementById('aiModelSelect'),
        deepThinkingToggle: document.getElementById('deepThinkingToggle'),
        aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
        aiSystemPrompt: document.getElementById('aiSystemPrompt'),
        saveAiSettingsBtn: document.getElementById('saveAiSettingsBtn'),
        testAiConnectionBtn: document.getElementById('testAiConnectionBtn')
    };

    // DeepSeek AI开关事件
    if (elements.aiToggle) {
        elements.aiToggle.addEventListener('change', function() {
            aiSettings.deepseek.enabled = this.checked;
            updateDeepSeekStatus();
            saveDeepSeekSettings();
            showNotification(`DeepSeek AI回复功能已${aiSettings.deepseek.enabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // 保存DeepSeek API密钥事件
    if (elements.saveApiKeyBtn) {
        elements.saveApiKeyBtn.addEventListener('click', async function() {
            const newApiKey = document.getElementById('aiApiKeyInput').value.trim();
            if (!newApiKey) {
                showNotification("请输入DeepSeek API密钥", 'error');
                return;
            }

            // 检查是否已存在
            if (aiSettings.deepseek.apiKeys.includes(newApiKey)) {
                showNotification("该DeepSeek API密钥已存在", 'error');
                return;
            }

            // 禁用保存按钮，显示验证中状态
            const originalText = this.textContent;
            this.disabled = true;
            this.textContent = '验证中...';

            try {
                // 验证API密钥
                showNotification("正在验证DeepSeek API密钥...", 'info');
                const validationResult = await validateApiKey(newApiKey);

                if (validationResult.valid) {
                    // 验证成功，保存密钥
                    aiSettings.deepseek.apiKeys.push(newApiKey);
                    aiSettings.deepseek.apiKeyStatus.push(true);

                    // 清空输入框
                    document.getElementById('aiApiKeyInput').value = '';

                    // 重新渲染列表
                    renderDeepSeekApiKeysList();
                    updateDeepSeekStatus();

                    // 保存设置到本地存储
                    saveDeepSeekSettings();

                    showNotification("DeepSeek API密钥验证成功并已保存", 'success');
                } else {
                    // 验证失败
                    showNotification(`DeepSeek API密钥验证失败: ${validationResult.error}`, 'error');
                }
            } catch (error) {
                console.error('DeepSeek API密钥验证过程中发生异常:', error);
                showNotification(`验证过程中发生错误: ${error.message}`, 'error');
            } finally {
                // 恢复保存按钮状态
                this.disabled = false;
                this.textContent = originalText;
            }
        });
    }

    // DeepSeek模型选择事件
    if (elements.aiModelSelect) {
        elements.aiModelSelect.addEventListener('change', function() {
            aiSettings.deepseek.model = this.value;
            saveDeepSeekSettings();
            showNotification(`DeepSeek已切换到模型: ${this.value}`, 'success');
        });
    }

    // DeepSeek深度思考开关事件
    if (elements.deepThinkingToggle) {
        elements.deepThinkingToggle.addEventListener('change', function() {
            aiSettings.deepseek.deepThinkingEnabled = this.checked;
            saveDeepSeekSettings();
            showNotification(`DeepSeek深度思考(R1)功能已${aiSettings.deepseek.deepThinkingEnabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // DeepSeek回复延迟事件
    if (elements.aiReplyDelayInput) {
        elements.aiReplyDelayInput.addEventListener('change', function() {
            aiSettings.deepseek.replyDelay = parseInt(this.value) || 0;
            saveDeepSeekSettings();
            showNotification(`DeepSeek回复延迟已设置为 ${aiSettings.deepseek.replyDelay} 秒`, 'success');
        });
    }

    // DeepSeek系统提示词事件
    if (elements.aiSystemPrompt) {
        elements.aiSystemPrompt.addEventListener('change', function() {
            aiSettings.deepseek.systemPrompt = this.value;
            saveDeepSeekSettings();
        });
    }

    // DeepSeek保存设置按钮事件
    if (elements.saveAiSettingsBtn) {
        elements.saveAiSettingsBtn.addEventListener('click', function() {
            saveDeepSeekSettings();
            showNotification('DeepSeek设置已保存', 'success');
        });
    }

    // DeepSeek测试连接按钮事件
    if (elements.testAiConnectionBtn) {
        elements.testAiConnectionBtn.addEventListener('click', async function() {
            await testDeepSeekConnection();
        });
    }
}

// 绑定豆包事件
function bindDoubaoEvents() {
    const elements = {
        doubaoToggle: document.getElementById('doubaoToggle'),
        saveDoubaoApiKeyBtn: document.getElementById('saveDoubaoApiKeyBtn'),
        doubaoModelSelect: document.getElementById('doubaoModelSelect'),
        doubaoThinkingToggle: document.getElementById('doubaoThinkingToggle'),
        doubaoReplyDelayInput: document.getElementById('doubaoReplyDelayInput'),
        doubaoSystemPrompt: document.getElementById('doubaoSystemPrompt'),
        saveDoubaoSettingsBtn: document.getElementById('saveDoubaoSettingsBtn'),
        testDoubaoConnectionBtn: document.getElementById('testDoubaoConnectionBtn')
    };

    // 豆包AI开关事件
    if (elements.doubaoToggle) {
        elements.doubaoToggle.addEventListener('change', function() {
            aiSettings.doubao.enabled = this.checked;
            updateDoubaoStatus();
            saveDoubaoSettings();
            showNotification(`豆包AI回复功能已${aiSettings.doubao.enabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // 保存豆包API密钥事件
    if (elements.saveDoubaoApiKeyBtn) {
        elements.saveDoubaoApiKeyBtn.addEventListener('click', async function() {
            const newApiKey = document.getElementById('doubaoApiKeyInput').value.trim();
            if (!newApiKey) {
                showNotification("请输入豆包API密钥", 'error');
                return;
            }

            // 检查是否已存在
            if (aiSettings.doubao.apiKeys.includes(newApiKey)) {
                showNotification("该豆包API密钥已存在", 'error');
                return;
            }

            // 禁用保存按钮，显示验证中状态
            const originalText = this.textContent;
            this.disabled = true;
            this.textContent = '验证中...';

            try {
                // 验证API密钥
                showNotification("正在验证豆包API密钥...", 'info');
                const validationResult = await validateDoubaoApiKey(newApiKey);

                if (validationResult.valid) {
                    // 验证成功，保存密钥
                    aiSettings.doubao.apiKeys.push(newApiKey);
                    aiSettings.doubao.apiKeyStatus.push(true);

                    // 清空输入框
                    document.getElementById('doubaoApiKeyInput').value = '';

                    // 重新渲染列表
                    renderDoubaoApiKeysList();
                    updateDoubaoStatus();

                    // 保存设置到本地存储
                    saveDoubaoSettings();

                    showNotification("豆包API密钥验证成功并已保存", 'success');
                } else {
                    // 验证失败
                    showNotification(`豆包API密钥验证失败: ${validationResult.error}`, 'error');
                }
            } catch (error) {
                console.error('豆包API密钥验证过程中发生异常:', error);
                showNotification(`验证过程中发生错误: ${error.message}`, 'error');
            } finally {
                // 恢复保存按钮状态
                this.disabled = false;
                this.textContent = originalText;
            }
        });
    }

    // 豆包模型选择事件
    if (elements.doubaoModelSelect) {
        elements.doubaoModelSelect.addEventListener('change', function() {
            aiSettings.doubao.model = this.value;
            saveDoubaoSettings();
            showNotification(`豆包已切换到模型: ${this.value}`, 'success');
        });
    }

    // 豆包思考开关事件
    if (elements.doubaoThinkingToggle) {
        elements.doubaoThinkingToggle.addEventListener('change', function() {
            aiSettings.doubao.thinkingEnabled = this.checked;
            saveDoubaoSettings();
            showNotification(`豆包深度思考功能已${aiSettings.doubao.thinkingEnabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // 豆包回复延迟事件
    if (elements.doubaoReplyDelayInput) {
        elements.doubaoReplyDelayInput.addEventListener('change', function() {
            aiSettings.doubao.replyDelay = parseInt(this.value) || 0;
            saveDoubaoSettings();
            showNotification(`豆包回复延迟已设置为 ${aiSettings.doubao.replyDelay} 秒`, 'success');
        });
    }

    // 豆包系统提示词事件
    if (elements.doubaoSystemPrompt) {
        elements.doubaoSystemPrompt.addEventListener('change', function() {
            aiSettings.doubao.systemPrompt = this.value;
            saveDoubaoSettings();
        });
    }

    // 豆包保存设置按钮事件
    if (elements.saveDoubaoSettingsBtn) {
        elements.saveDoubaoSettingsBtn.addEventListener('click', function() {
            saveDoubaoSettings();
            showNotification('豆包设置已保存', 'success');
        });
    }

    // 豆包测试连接按钮事件
    if (elements.testDoubaoConnectionBtn) {
        elements.testDoubaoConnectionBtn.addEventListener('click', async function() {
            await testDoubaoConnection();
        });
    }
}

// 验证API密钥
async function validateApiKey(apiKey) {
    if (!apiKey || !apiKey.startsWith('sk-')) {
        return {
            valid: false,
            error: 'API密钥格式不正确，应以sk-开头'
        };
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        const response = await fetch('https://api.deepseek.com/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const data = await response.json();
            return {
                valid: true,
                models: data.data || [],
                message: 'API密钥验证成功'
            };
        } else if (response.status === 401) {
            return {
                valid: false,
                error: 'API密钥无效或已过期'
            };
        } else if (response.status === 429) {
            return {
                valid: false,
                error: 'API请求频率过高，请稍后再试'
            };
        } else {
            const errorText = await response.text();
            return {
                valid: false,
                error: `API验证失败 (${response.status}): ${errorText}`
            };
        }
    } catch (error) {
        console.error('API密钥验证异常:', error);
        if (error.name === 'AbortError') {
            return {
                valid: false,
                error: '验证超时，请检查网络连接'
            };
        } else {
            return {
                valid: false,
                error: `验证过程中发生错误: ${error.message}`
            };
        }
    }
}

// 验证豆包API密钥
async function validateDoubaoApiKey(apiKey) {
    if (!apiKey || apiKey.trim() === '') {
        return {
            valid: false,
            error: 'API密钥不能为空'
        };
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        // 使用豆包API进行验证
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'doubao-seed-1-6-250615',
                messages: [
                    {
                        role: 'user',
                        content: 'test'
                    }
                ],
                max_tokens: 1
            }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            return {
                valid: true,
                message: '豆包API密钥验证成功'
            };
        } else if (response.status === 401) {
            return {
                valid: false,
                error: '豆包API密钥无效或已过期'
            };
        } else if (response.status === 429) {
            return {
                valid: false,
                error: 'API请求频率过高，请稍后再试'
            };
        } else {
            const errorText = await response.text();
            return {
                valid: false,
                error: `豆包API验证失败 (${response.status}): ${errorText}`
            };
        }
    } catch (error) {
        console.error('豆包API密钥验证异常:', error);
        if (error.name === 'AbortError') {
            return {
                valid: false,
                error: '验证超时，请检查网络连接'
            };
        } else {
            return {
                valid: false,
                error: `验证过程中发生错误: ${error.message}`
            };
        }
    }
}

// 渲染DeepSeek API密钥列表
function renderDeepSeekApiKeysList() {
    const container = document.getElementById('apiKeysStatus');
    if (!container) return;

    if (aiSettings.deepseek.apiKeys.length === 0) {
        container.innerHTML = '<div class="api-keys-status-display">暂无保存的DeepSeek API密钥</div>';
        return;
    }

    // 直接显示所有密钥
    const keysHtml = aiSettings.deepseek.apiKeys.map((key, index) => {
        const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
        const status = aiSettings.deepseek.apiKeyStatus[index] ? '可用' : '不可用';
        const statusClass = aiSettings.deepseek.apiKeyStatus[index] ? 'success' : 'error';

        return `
            <div class="api-key-item">
                <div class="api-key-info">
                    <div class="api-key-text">${maskedKey}</div>
                    <div class="api-key-status ${statusClass}">${status}</div>
                </div>
                <div class="api-key-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="validateDeepSeekApiKeyDirect(${index})">验证</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteDeepSeekApiKeyDirect(${index})">删除</button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="api-keys-list-container">
            <div class="api-keys-header">DeepSeek API密钥列表</div>
            <div class="api-keys-list">
                ${keysHtml}
            </div>
        </div>
    `;
}

// 渲染豆包API密钥列表
function renderDoubaoApiKeysList() {
    const container = document.getElementById('doubaoApiKeysStatus');
    if (!container) return;

    if (aiSettings.doubao.apiKeys.length === 0) {
        container.innerHTML = '<div class="api-keys-status-display">暂无保存的豆包API密钥</div>';
        return;
    }

    // 直接显示所有密钥
    const keysHtml = aiSettings.doubao.apiKeys.map((key, index) => {
        const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
        const status = aiSettings.doubao.apiKeyStatus[index] ? '可用' : '不可用';
        const statusClass = aiSettings.doubao.apiKeyStatus[index] ? 'success' : 'error';

        return `
            <div class="api-key-item">
                <div class="api-key-info">
                    <div class="api-key-text">${maskedKey}</div>
                    <div class="api-key-status ${statusClass}">${status}</div>
                </div>
                <div class="api-key-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="validateDoubaoApiKeyDirect(${index})">验证</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteDoubaoApiKeyDirect(${index})">删除</button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="api-keys-list-container">
            <div class="api-keys-header">豆包API密钥列表</div>
            <div class="api-keys-list">
                ${keysHtml}
            </div>
        </div>
    `;
}

// 兼容性函数（保持向后兼容）
function renderApiKeysList() {
    renderDeepSeekApiKeysList();
    renderDoubaoApiKeysList();
}

// 更新DeepSeek状态显示
function updateDeepSeekStatus() {
    const indicator = document.getElementById('aiStatusIndicator');
    const statusText = document.getElementById('aiStatusText');

    if (!indicator || !statusText) return;

    // 重置状态
    indicator.classList.remove('active');

    if (!aiSettings.deepseek.enabled) {
        statusText.textContent = 'DeepSeek AI已禁用';
    } else if (aiSettings.deepseek.apiKeys.length === 0) {
        statusText.textContent = 'API密钥未设置';
    } else {
        statusText.textContent = 'DeepSeek AI已启用';
        indicator.classList.add('active');
    }
}

// 更新豆包状态显示
function updateDoubaoStatus() {
    const indicator = document.getElementById('doubaoStatusIndicator');
    const statusText = document.getElementById('doubaoStatusText');

    if (!indicator || !statusText) return;

    // 重置状态
    indicator.classList.remove('active');

    if (!aiSettings.doubao.enabled) {
        statusText.textContent = '豆包AI已禁用';
    } else if (aiSettings.doubao.apiKeys.length === 0) {
        statusText.textContent = 'API密钥未设置';
    } else {
        statusText.textContent = '豆包AI已启用';
        indicator.classList.add('active');
    }
}

// 兼容性函数（保持向后兼容）
function updateAIStatus() {
    updateDeepSeekStatus();
    updateDoubaoStatus();
}

// 测试DeepSeek连接
async function testDeepSeekConnection() {
    if (aiSettings.deepseek.apiKeys.length === 0) {
        showNotification('请先添加DeepSeek API密钥', 'error');
        return;
    }

    const btn = document.getElementById('testAiConnectionBtn');
    const originalText = btn.textContent;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';

    try {
        const currentApiKey = aiSettings.deepseek.apiKeys[aiSettings.deepseek.currentApiKeyIndex] || aiSettings.deepseek.apiKeys[0];
        const result = await validateApiKey(currentApiKey);

        if (result.valid) {
            showNotification('DeepSeek API连接测试成功', 'success');
        } else {
            showNotification(`DeepSeek API连接测试失败: ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`DeepSeek测试过程中发生错误: ${error.message}`, 'error');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// 测试豆包连接
async function testDoubaoConnection() {
    if (aiSettings.doubao.apiKeys.length === 0) {
        showNotification('请先添加豆包API密钥', 'error');
        return;
    }

    const btn = document.getElementById('testDoubaoConnectionBtn');
    const originalText = btn.textContent;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';

    try {
        const currentApiKey = aiSettings.doubao.apiKeys[aiSettings.doubao.currentApiKeyIndex] || aiSettings.doubao.apiKeys[0];
        const result = await validateDoubaoApiKey(currentApiKey);

        if (result.valid) {
            showNotification('豆包API连接测试成功', 'success');
        } else {
            showNotification(`豆包API连接测试失败: ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`豆包测试过程中发生错误: ${error.message}`, 'error');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// 测试AI连接（兼容性函数）
async function testAIConnection() {
    if (currentTab === 'deepseek_settings') {
        await testDeepSeekConnection();
    } else if (currentTab === 'doubao_settings') {
        await testDoubaoConnection();
    }
}

// 打开API密钥管理弹窗
function openApiKeysModal() {
    const modal = document.getElementById('apiKeysModal');
    if (modal) {
        modal.style.display = 'block';
        renderApiKeysModal();
    }
}

// 关闭API密钥管理弹窗
function closeApiKeysModal() {
    const modal = document.getElementById('apiKeysModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 渲染DeepSeek弹窗中的API密钥列表
function renderApiKeysModal() {
    const container = document.getElementById('apiKeysModalList');
    if (!container) return;

    if (aiSettings.deepseek.apiKeys.length === 0) {
        container.innerHTML = '<div class="empty-api-keys">暂无保存的DeepSeek API密钥</div>';
        return;
    }

    container.innerHTML = aiSettings.deepseek.apiKeys.map((key, index) => {
        const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
        const status = aiSettings.deepseek.apiKeyStatus[index] ? '可用' : '不可用';
        const statusClass = aiSettings.deepseek.apiKeyStatus[index] ? 'success' : 'error';

        return `
            <div class="api-key-item">
                <div class="api-key-info">
                    <div class="api-key-text">${maskedKey}</div>
                    <div class="api-key-status ${statusClass}">${status}</div>
                </div>
                <div class="api-key-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="validateApiKeyFromModal(${index})">验证</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteApiKeyFromModal(${index})">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 从DeepSeek弹窗删除API密钥
function deleteApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.deepseek.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const deletedKey = aiSettings.deepseek.apiKeys[index].substring(0, 8) + '...';

    // 删除密钥和状态
    aiSettings.deepseek.apiKeys.splice(index, 1);
    aiSettings.deepseek.apiKeyStatus.splice(index, 1);

    // 调整当前索引
    if (aiSettings.deepseek.currentApiKeyIndex >= aiSettings.deepseek.apiKeys.length) {
        aiSettings.deepseek.currentApiKeyIndex = Math.max(0, aiSettings.deepseek.apiKeys.length - 1);
    }

    // 重新渲染
    renderApiKeysModal();
    renderDeepSeekApiKeysList();
    updateDeepSeekStatus();

    showNotification(`DeepSeek API密钥 ${deletedKey} 已删除`, 'success');
}

// 从DeepSeek弹窗验证API密钥
async function validateApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.deepseek.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const apiKey = aiSettings.deepseek.apiKeys[index];
    const maskedKey = apiKey.substring(0, 8) + '...';

    try {
        showNotification(`开始验证DeepSeek API密钥 ${maskedKey}`, 'info');
        const validationResult = await validateApiKey(apiKey);

        if (validationResult.valid) {
            // 验证成功，更新状态
            aiSettings.deepseek.apiKeyStatus[index] = true;
            showNotification(`DeepSeek API密钥 ${maskedKey} 验证成功`, 'success');
        } else {
            // 验证失败，更新状态
            aiSettings.deepseek.apiKeyStatus[index] = false;
            showNotification(`DeepSeek API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');
        }

        // 重新渲染
        renderApiKeysModal();
        renderDeepSeekApiKeysList();
        updateDeepSeekStatus();

    } catch (error) {
        console.error('DeepSeek API密钥验证过程中发生异常:', error);
        showNotification(`DeepSeek API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

        // 验证异常时标记为不可用
        aiSettings.deepseek.apiKeyStatus[index] = false;
        renderApiKeysModal();
        renderDeepSeekApiKeysList();
        updateDeepSeekStatus();
    }
}

// DeepSeek直接操作API密钥函数
// 直接验证DeepSeek API密钥
async function validateDeepSeekApiKeyDirect(index) {
    if (index < 0 || index >= aiSettings.deepseek.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const apiKey = aiSettings.deepseek.apiKeys[index];
    const maskedKey = apiKey.substring(0, 8) + '...';

    try {
        showNotification(`开始验证DeepSeek API密钥 ${maskedKey}`, 'info');
        const validationResult = await validateApiKey(apiKey);

        if (validationResult.valid) {
            // 验证成功，更新状态
            aiSettings.deepseek.apiKeyStatus[index] = true;
            showNotification(`DeepSeek API密钥 ${maskedKey} 验证成功`, 'success');
        } else {
            // 验证失败，更新状态
            aiSettings.deepseek.apiKeyStatus[index] = false;
            showNotification(`DeepSeek API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');
        }

        // 重新渲染
        renderDeepSeekApiKeysList();
        updateDeepSeekStatus();
        saveDeepSeekSettings(); // 保存状态

    } catch (error) {
        console.error('DeepSeek API密钥验证过程中发生异常:', error);
        showNotification(`DeepSeek API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

        // 验证异常时标记为不可用
        aiSettings.deepseek.apiKeyStatus[index] = false;
        renderDeepSeekApiKeysList();
        updateDeepSeekStatus();
        saveDeepSeekSettings(); // 保存状态
    }
}

// 直接删除DeepSeek API密钥
function deleteDeepSeekApiKeyDirect(index) {
    if (index < 0 || index >= aiSettings.deepseek.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const deletedKey = aiSettings.deepseek.apiKeys[index].substring(0, 8) + '...';

    // 删除密钥和状态
    aiSettings.deepseek.apiKeys.splice(index, 1);
    aiSettings.deepseek.apiKeyStatus.splice(index, 1);

    // 调整当前索引
    if (aiSettings.deepseek.currentApiKeyIndex >= aiSettings.deepseek.apiKeys.length) {
        aiSettings.deepseek.currentApiKeyIndex = Math.max(0, aiSettings.deepseek.apiKeys.length - 1);
    }

    // 重新渲染
    renderDeepSeekApiKeysList();
    updateDeepSeekStatus();
    saveDeepSeekSettings(); // 保存状态

    showNotification(`DeepSeek API密钥 ${deletedKey} 已删除`, 'success');
}

// 豆包直接操作API密钥函数
// 直接验证豆包API密钥
async function validateDoubaoApiKeyDirect(index) {
    if (index < 0 || index >= aiSettings.doubao.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const apiKey = aiSettings.doubao.apiKeys[index];
    const maskedKey = apiKey.substring(0, 8) + '...';

    try {
        showNotification(`开始验证豆包API密钥 ${maskedKey}`, 'info');
        const validationResult = await validateDoubaoApiKey(apiKey);

        if (validationResult.valid) {
            // 验证成功，更新状态
            aiSettings.doubao.apiKeyStatus[index] = true;
            showNotification(`豆包API密钥 ${maskedKey} 验证成功`, 'success');
        } else {
            // 验证失败，更新状态
            aiSettings.doubao.apiKeyStatus[index] = false;
            showNotification(`豆包API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');
        }

        // 重新渲染
        renderDoubaoApiKeysList();
        updateDoubaoStatus();
        saveDoubaoSettings(); // 保存状态

    } catch (error) {
        console.error('豆包API密钥验证过程中发生异常:', error);
        showNotification(`豆包API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

        // 验证异常时标记为不可用
        aiSettings.doubao.apiKeyStatus[index] = false;
        renderDoubaoApiKeysList();
        updateDoubaoStatus();
        saveDoubaoSettings(); // 保存状态
    }
}

// 直接删除豆包API密钥
function deleteDoubaoApiKeyDirect(index) {
    if (index < 0 || index >= aiSettings.doubao.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const deletedKey = aiSettings.doubao.apiKeys[index].substring(0, 8) + '...';

    // 删除密钥和状态
    aiSettings.doubao.apiKeys.splice(index, 1);
    aiSettings.doubao.apiKeyStatus.splice(index, 1);

    // 调整当前索引
    if (aiSettings.doubao.currentApiKeyIndex >= aiSettings.doubao.apiKeys.length) {
        aiSettings.doubao.currentApiKeyIndex = Math.max(0, aiSettings.doubao.apiKeys.length - 1);
    }

    // 重新渲染
    renderDoubaoApiKeysList();
    updateDoubaoStatus();
    saveDoubaoSettings(); // 保存状态

    showNotification(`豆包API密钥 ${deletedKey} 已删除`, 'success');
}

// 豆包API密钥管理弹窗函数
// 打开豆包API密钥管理弹窗
function openDoubaoApiKeysModal() {
    const modal = document.getElementById('doubaoApiKeysModal');
    if (modal) {
        modal.style.display = 'block';
        renderDoubaoApiKeysModal();
    }
}

// 关闭豆包API密钥管理弹窗
function closeDoubaoApiKeysModal() {
    const modal = document.getElementById('doubaoApiKeysModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 渲染豆包弹窗中的API密钥列表
function renderDoubaoApiKeysModal() {
    const container = document.getElementById('doubaoApiKeysModalList');
    if (!container) return;

    if (aiSettings.doubao.apiKeys.length === 0) {
        container.innerHTML = '<div class="empty-api-keys">暂无保存的豆包API密钥</div>';
        return;
    }

    container.innerHTML = aiSettings.doubao.apiKeys.map((key, index) => {
        const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
        const status = aiSettings.doubao.apiKeyStatus[index] ? '可用' : '不可用';
        const statusClass = aiSettings.doubao.apiKeyStatus[index] ? 'success' : 'error';

        return `
            <div class="api-key-item">
                <div class="api-key-info">
                    <div class="api-key-text">${maskedKey}</div>
                    <div class="api-key-status ${statusClass}">${status}</div>
                </div>
                <div class="api-key-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="validateDoubaoApiKeyFromModal(${index})">验证</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteDoubaoApiKeyFromModal(${index})">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 从豆包弹窗删除API密钥
function deleteDoubaoApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.doubao.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const deletedKey = aiSettings.doubao.apiKeys[index].substring(0, 8) + '...';

    // 删除密钥和状态
    aiSettings.doubao.apiKeys.splice(index, 1);
    aiSettings.doubao.apiKeyStatus.splice(index, 1);

    // 调整当前索引
    if (aiSettings.doubao.currentApiKeyIndex >= aiSettings.doubao.apiKeys.length) {
        aiSettings.doubao.currentApiKeyIndex = Math.max(0, aiSettings.doubao.apiKeys.length - 1);
    }

    // 重新渲染
    renderDoubaoApiKeysModal();
    renderDoubaoApiKeysList();
    updateDoubaoStatus();

    showNotification(`豆包API密钥 ${deletedKey} 已删除`, 'success');
}

// 从豆包弹窗验证API密钥
async function validateDoubaoApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.doubao.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const apiKey = aiSettings.doubao.apiKeys[index];
    const maskedKey = apiKey.substring(0, 8) + '...';

    try {
        showNotification(`开始验证豆包API密钥 ${maskedKey}`, 'info');
        const validationResult = await validateDoubaoApiKey(apiKey);

        if (validationResult.valid) {
            // 验证成功，更新状态
            aiSettings.doubao.apiKeyStatus[index] = true;
            showNotification(`豆包API密钥 ${maskedKey} 验证成功`, 'success');
        } else {
            // 验证失败，更新状态
            aiSettings.doubao.apiKeyStatus[index] = false;
            showNotification(`豆包API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');
        }

        // 重新渲染
        renderDoubaoApiKeysModal();
        renderDoubaoApiKeysList();
        updateDoubaoStatus();

    } catch (error) {
        console.error('豆包API密钥验证过程中发生异常:', error);
        showNotification(`豆包API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

        // 验证异常时标记为不可用
        aiSettings.doubao.apiKeyStatus[index] = false;
        renderDoubaoApiKeysModal();
        renderDoubaoApiKeysList();
        updateDoubaoStatus();
    }
}

// 加载AI设置
function loadAISettings() {
    // 这里可以从服务器加载设置，目前使用本地存储
    const saved = localStorage.getItem('aiServiceSettings');
    if (saved) {
        try {
            const settings = JSON.parse(saved);

            // 兼容旧版本数据结构
            if (settings.enabled !== undefined && !settings.deepseek) {
                // 旧版本数据，转换为新结构
                aiSettings.deepseek = {
                    enabled: settings.enabled || false,
                    apiKeys: settings.apiKeys || [],
                    apiKeyStatus: settings.apiKeyStatus || [],
                    currentApiKeyIndex: settings.currentApiKeyIndex || 0,
                    model: settings.model || 'deepseek-chat',
                    deepThinkingEnabled: settings.deepThinkingEnabled || false,
                    replyDelay: settings.replyDelay || 0,
                    systemPrompt: settings.systemPrompt || aiSettings.deepseek.systemPrompt
                };
            } else {
                // 新版本数据结构
                Object.assign(aiSettings, settings);
            }

            // 更新DeepSeek UI
            const deepseekElements = {
                aiToggle: document.getElementById('aiToggle'),
                aiModelSelect: document.getElementById('aiModelSelect'),
                deepThinkingToggle: document.getElementById('deepThinkingToggle'),
                aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
                aiSystemPrompt: document.getElementById('aiSystemPrompt')
            };

            if (deepseekElements.aiToggle) deepseekElements.aiToggle.checked = aiSettings.deepseek.enabled;
            if (deepseekElements.aiModelSelect) deepseekElements.aiModelSelect.value = aiSettings.deepseek.model;
            if (deepseekElements.deepThinkingToggle) deepseekElements.deepThinkingToggle.checked = aiSettings.deepseek.deepThinkingEnabled;
            if (deepseekElements.aiReplyDelayInput) deepseekElements.aiReplyDelayInput.value = aiSettings.deepseek.replyDelay;
            if (deepseekElements.aiSystemPrompt) deepseekElements.aiSystemPrompt.value = aiSettings.deepseek.systemPrompt;

            // 更新豆包UI
            const doubaoElements = {
                doubaoToggle: document.getElementById('doubaoToggle'),
                doubaoModelSelect: document.getElementById('doubaoModelSelect'),
                doubaoThinkingToggle: document.getElementById('doubaoThinkingToggle'),
                doubaoReplyDelayInput: document.getElementById('doubaoReplyDelayInput'),
                doubaoSystemPrompt: document.getElementById('doubaoSystemPrompt')
            };

            if (doubaoElements.doubaoToggle) doubaoElements.doubaoToggle.checked = aiSettings.doubao.enabled;
            if (doubaoElements.doubaoModelSelect) doubaoElements.doubaoModelSelect.value = aiSettings.doubao.model;
            if (doubaoElements.doubaoThinkingToggle) doubaoElements.doubaoThinkingToggle.checked = aiSettings.doubao.thinkingEnabled;
            if (doubaoElements.doubaoReplyDelayInput) doubaoElements.doubaoReplyDelayInput.value = aiSettings.doubao.replyDelay;
            if (doubaoElements.doubaoSystemPrompt) doubaoElements.doubaoSystemPrompt.value = aiSettings.doubao.systemPrompt;

            renderApiKeysList();
            updateAIStatus();

            // 更新管理模块
            renderDeepSeekManagement();
            renderDoubaoManagement();
        } catch (error) {
            console.error('加载AI设置失败:', error);
        }
    }
}

// 保存DeepSeek设置
function saveDeepSeekSettings() {
    // 收集当前设置
    aiSettings.deepseek.enabled = document.getElementById('aiToggle').checked;
    aiSettings.deepseek.model = document.getElementById('aiModelSelect').value;
    aiSettings.deepseek.deepThinkingEnabled = document.getElementById('deepThinkingToggle').checked;
    aiSettings.deepseek.replyDelay = parseInt(document.getElementById('aiReplyDelayInput').value) || 0;
    aiSettings.deepseek.systemPrompt = document.getElementById('aiSystemPrompt').value;

    // 保存到本地存储
    localStorage.setItem('aiServiceSettings', JSON.stringify(aiSettings));

    // 更新管理模块显示
    renderDeepSeekManagement();

    // 这里可以添加保存到服务器的逻辑
}

// 保存豆包设置
function saveDoubaoSettings() {
    // 收集当前设置
    aiSettings.doubao.enabled = document.getElementById('doubaoToggle').checked;
    aiSettings.doubao.model = document.getElementById('doubaoModelSelect').value;
    aiSettings.doubao.thinkingEnabled = document.getElementById('doubaoThinkingToggle').checked;
    aiSettings.doubao.replyDelay = parseInt(document.getElementById('doubaoReplyDelayInput').value) || 0;
    aiSettings.doubao.systemPrompt = document.getElementById('doubaoSystemPrompt').value;

    // 保存到本地存储
    localStorage.setItem('aiServiceSettings', JSON.stringify(aiSettings));

    // 更新管理模块显示
    renderDoubaoManagement();

    // 这里可以添加保存到服务器的逻辑
}

// 保存AI设置（兼容性函数）
function saveAISettings() {
    saveDeepSeekSettings();
    saveDoubaoSettings();
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (!notification) return;

    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

// 初始化弹窗事件
document.addEventListener('DOMContentLoaded', function() {
    // DeepSeek API密钥弹窗事件
    const modal = document.getElementById('apiKeysModal');
    const closeBtn = document.getElementById('closeApiKeysModal');

    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', closeApiKeysModal);
    }

    // 点击弹窗外部关闭
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeApiKeysModal();
            }
        });
    }

    // 豆包API密钥弹窗事件
    const doubaoModal = document.getElementById('doubaoApiKeysModal');
    const doubaoCloseBtn = document.getElementById('closeDoubaoApiKeysModal');

    // 豆包关闭按钮事件
    if (doubaoCloseBtn) {
        doubaoCloseBtn.addEventListener('click', closeDoubaoApiKeysModal);
    }

    // 点击豆包弹窗外部关闭
    if (doubaoModal) {
        doubaoModal.addEventListener('click', function(e) {
            if (e.target === doubaoModal) {
                closeDoubaoApiKeysModal();
            }
        });
    }

    // 初始化管理模块
    setTimeout(() => {
        renderDeepSeekManagement();
        renderDoubaoManagement();
    }, 100);
});

// ==================== 设置管理模块功能 ====================

// 渲染DeepSeek管理模块
window.renderDeepSeekManagement = function() {
    const container = document.getElementById('deepseekManagementContainer');
    const noSettingsMsg = document.getElementById('deepseekNoSettings');

    if (!container) return;

    // 检查是否有保存的设置 - 简化条件，只要有任何配置就显示
    const hasSettings = aiSettings.deepseek.enabled ||
                       aiSettings.deepseek.apiKeys.length > 0 ||
                       aiSettings.deepseek.replyDelay > 0 ||
                       aiSettings.deepseek.deepThinkingEnabled ||
                       aiSettings.deepseek.model !== 'deepseek-chat';

    if (!hasSettings) {
        noSettingsMsg.style.display = 'flex';
        return;
    }

    noSettingsMsg.style.display = 'none';

    // 创建设置项
    const settingsItem = createSettingsItem('deepseek', aiSettings.deepseek);
    container.innerHTML = settingsItem;
};

// 渲染豆包管理模块
window.renderDoubaoManagement = function() {
    const container = document.getElementById('doubaoManagementContainer');
    const noSettingsMsg = document.getElementById('doubaoNoSettings');

    if (!container) return;

    // 检查是否有保存的设置 - 简化条件，只要有任何配置就显示
    const hasSettings = aiSettings.doubao.enabled ||
                       aiSettings.doubao.apiKeys.length > 0 ||
                       aiSettings.doubao.replyDelay > 0 ||
                       aiSettings.doubao.thinkingEnabled ||
                       aiSettings.doubao.model !== 'doubao-seed-1-6-250615';

    if (!hasSettings) {
        noSettingsMsg.style.display = 'flex';
        return;
    }

    noSettingsMsg.style.display = 'none';

    // 创建设置项
    const settingsItem = createSettingsItem('doubao', aiSettings.doubao);
    container.innerHTML = settingsItem;
};

// 创建设置项HTML
window.createSettingsItem = function(type, settings) {
    const typeName = type === 'deepseek' ? 'DeepSeek' : '豆包';
    const enabledStatus = settings.enabled ? 'enabled' : 'disabled';
    const enabledText = settings.enabled ? '已启用' : '已禁用';
    const apiKeysCount = settings.apiKeys.length;
    const validApiKeysCount = settings.apiKeyStatus.filter(status => status).length;

    return `
        <div class="settings-item">
            <div class="settings-item-header">
                <h4 class="settings-item-title">
                    <i class="fas fa-${type === 'deepseek' ? 'brain' : 'robot'}"></i>
                    ${typeName} 配置
                </h4>
                <div class="settings-item-actions">
                    <button type="button" class="btn-edit" onclick="editSettings('${type}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn-delete" onclick="deleteSettings('${type}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            <div class="settings-item-content">
                <div class="settings-detail">
                    <div class="settings-detail-label">状态</div>
                    <div class="settings-detail-value">
                        <span class="settings-status ${enabledStatus}">${enabledText}</span>
                    </div>
                </div>
                <div class="settings-detail">
                    <div class="settings-detail-label">API密钥</div>
                    <div class="settings-detail-value">${validApiKeysCount}/${apiKeysCount} 个可用</div>
                </div>
                <div class="settings-detail">
                    <div class="settings-detail-label">模型</div>
                    <div class="settings-detail-value">${getModelDisplayName(type, settings.model)}</div>
                </div>
                <div class="settings-detail">
                    <div class="settings-detail-label">回复延迟</div>
                    <div class="settings-detail-value">${settings.replyDelay}秒</div>
                </div>
                <div class="settings-detail">
                    <div class="settings-detail-label">深度思考</div>
                    <div class="settings-detail-value">${(type === 'deepseek' ? settings.deepThinkingEnabled : settings.thinkingEnabled) ? '已启用' : '已禁用'}</div>
                </div>
                <div class="settings-detail">
                    <div class="settings-detail-label">系统提示词</div>
                    <div class="settings-detail-value">${settings.systemPrompt.substring(0, 50)}${settings.systemPrompt.length > 50 ? '...' : ''}</div>
                </div>
            </div>
        </div>
    `;
};

// 获取模型显示名称
window.getModelDisplayName = function(type, modelValue) {
    if (type === 'deepseek') {
        switch (modelValue) {
            case 'deepseek-chat': return 'DeepSeek Chat';
            case 'deepseek-reasoner': return 'DeepSeek-R1-0528';
            default: return modelValue;
        }
    } else {
        switch (modelValue) {
            case 'doubao-seed-1-6-250615': return '豆包 Seed 1.6';
            case 'doubao-1.5-vision-pro-250328': return '豆包 1.5 Vision Pro';
            case 'doubao-seed-1-6-thinking-250715': return '豆包 Seed 1.6 Thinking';
            default: return modelValue;
        }
    }
};

// 编辑设置 - 确保在全局作用域
window.editSettings = function(type) {
    console.log('editSettings called with type:', type);
    // 切换到对应的标签页
    const targetTab = type + '_settings';
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    // 移除所有活动状态
    navTabs.forEach(t => t.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // 添加当前活动状态
    const targetNavTab = document.querySelector(`[data-tab="${targetTab}"]`);
    const targetContent = document.getElementById(targetTab + '-content');

    if (targetNavTab && targetContent) {
        targetNavTab.classList.add('active');
        targetContent.classList.add('active');
        currentTab = targetTab;
        localStorage.setItem('currentAITab', currentTab);
    }

    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });

    showNotification(`已切换到${type === 'deepseek' ? 'DeepSeek' : '豆包'}设置页面`, 'info');
};

// 删除设置 - 确保在全局作用域
window.deleteSettings = function(type) {
    console.log('deleteSettings called with type:', type);
    const typeName = type === 'deepseek' ? 'DeepSeek' : '豆包';

    if (confirm(`确定要删除所有${typeName}设置吗？此操作不可撤销。`)) {
        // 重置设置
        if (type === 'deepseek') {
            aiSettings.deepseek = {
                enabled: false,
                apiKeys: [],
                apiKeyStatus: [],
                currentApiKeyIndex: 0,
                model: 'deepseek-chat',
                deepThinkingEnabled: false,
                replyDelay: 0,
                systemPrompt: '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
            };

            // 更新界面
            document.getElementById('aiToggle').checked = false;
            document.getElementById('aiModelSelect').value = 'deepseek-chat';
            document.getElementById('deepThinkingToggle').checked = false;
            document.getElementById('aiReplyDelayInput').value = '';
            document.getElementById('aiSystemPrompt').value = aiSettings.deepseek.systemPrompt;

            renderDeepSeekApiKeysList();
            updateDeepSeekStatus();
            renderDeepSeekManagement();
            saveDeepSeekSettings();
        } else {
            aiSettings.doubao = {
                enabled: false,
                apiKeys: [],
                apiKeyStatus: [],
                currentApiKeyIndex: 0,
                model: 'doubao-seed-1-6-250615',
                thinkingEnabled: false,
                replyDelay: 0,
                systemPrompt: '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
            };

            // 更新界面
            document.getElementById('doubaoToggle').checked = false;
            document.getElementById('doubaoModelSelect').value = 'doubao-seed-1-6-250615';
            document.getElementById('doubaoThinkingToggle').checked = false;
            document.getElementById('doubaoReplyDelayInput').value = '';
            document.getElementById('doubaoSystemPrompt').value = aiSettings.doubao.systemPrompt;

            renderDoubaoApiKeysList();
            updateDoubaoStatus();
            renderDoubaoManagement();
            saveDoubaoSettings();
        }

        showNotification(`${typeName}设置已删除`, 'success');
    }
};
</script>
